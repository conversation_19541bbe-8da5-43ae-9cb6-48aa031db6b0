@import "../../../theme.scss";

:host {
  display: block;
  padding: 1rem 0;
}

mat-form-field {
  width: 500px;
  margin: 0 auto;
  display: block;
}

mat-card {
  width: max-content;
  margin-bottom: 1rem;
  mat-card-content {
    font-size: 1.5rem;
  }
}

mat-accordion {
  mat-panel-title {
    font-size: 1.4rem;
    letter-spacing: 2px;
    font-weight: 500;
    color: $dark-accent-A200;
  }

  mat-panel-description {
    font-style: italic;
    font-size: 1.2rem;
    letter-spacing: 1px;
  }

  table {
    width: 100%;
    border: 1px solid rgba(255, 255, 255, 0.1);

    .mat-mdc-header-cell {
      font-style: italic;
      font-size: 1.2rem;
    }

    .mat-mdc-cell {
      font-size: 1.2rem;
    }
  }
}
