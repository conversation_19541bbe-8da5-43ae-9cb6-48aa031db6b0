@import "../../../theme.scss";

:host {
  display: flex;
  justify-content: space-between;
  height: 100%;
  width: 90%;
}

.register-form {
  width: 600px;

  mat-card {
    mat-card-header {
      display: flex;
      justify-content: center;

      mat-card-title {
        font-size: 2rem;
        color: $dark-accent-A200;
      }
    }
    mat-grid-tile {
      mat-form-field {
        width: 100%;
      }
    }

    mat-card-actions {
      button {
        margin: auto;
        width: 80%;
      }
      h4 {
        margin: 0;
        padding-right: 1rem;
      }
    }
  }
}

.library-info {
  width: 400px;
  padding: 1rem;
  border-left: 1px solid white;

  mat-list-item {
    height: auto;

    p {
      font-size: 1.2rem;
      font-style: italic;
      text-align: justify;
      white-space: normal;
    }
  }
}
