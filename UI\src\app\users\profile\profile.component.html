<mat-card>
    <mat-card-header>
        <mat-card-title>Profile Information</mat-card-title>
    </mat-card-header>

    <mat-card-content>
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8">

            <!-- Name Column -->
            <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef> Name </th>
                <td mat-cell *matCellDef="let element"> {{ element.name }} </td>
            </ng-container>

            <!-- Value Column -->
            <ng-container matColumnDef="value">
                <th mat-header-cell *matHeaderCellDef> Value </th>
                <td mat-cell *matCellDef="let element"> {{ element.value }}
                </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="columns" hidden></tr>
            <tr mat-row *matRowDef="let row; columns: columns"></tr>

        </table>
    </mat-card-content>
</mat-card>