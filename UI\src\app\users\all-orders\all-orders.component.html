<mat-card>
    <mat-card-content>
        <h1>Orders With Pending Returns (1)</h1>

        <button mat-raised-button color="accent" (click)="sendEmail()">
            Send Email About Pending Returns
        </button>
        <button mat-raised-button color="accent" (click)="blockUsers()">
            Block Overdue Users
        </button>
    </mat-card-content>

    <mat-card-footer>
        @if (showProgressBar) {
        <mat-progress-bar mode="indeterminate"
                          color="accent">
        </mat-progress-bar>
        }
    </mat-card-footer>
</mat-card>

<page-table [columns]="columnsForPendingReturns"
            [dataSource]="ordersWithPendingReturns">
</page-table>

<mat-card>
    <mat-card-content>
        <h1>Orders With Completed Returns (2)</h1>
    </mat-card-content>
</mat-card>

<page-table [columns]="columnsForCompletedReturns"
            [dataSource]="ordersWithCompletedReturns">
</page-table>