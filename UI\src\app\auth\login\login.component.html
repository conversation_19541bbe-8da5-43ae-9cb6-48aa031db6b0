<mat-card>
    <mat-card-header>
        <mat-card-title>Login Form</mat-card-title>
    </mat-card-header>

    <mat-card-content [formGroup]="loginForm">
        <mat-form-field color="accent">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email">
            <mat-error>
                Field is required.
            </mat-error>
        </mat-form-field>

        <mat-form-field color="accent">
            <mat-label>Password</mat-label>
            <input matInput [type]="hidePassword ? 'password' : 'text'"
                   formControlName="password">
            <button mat-icon-button matSuffix
                    (click)="hidePassword = !hidePassword">
                <mat-icon> {{hidePassword ? "visibility_off" : "visibility_on"}}
                </mat-icon>
            </button>
            <mat-error>
                Field is required.
            </mat-error>
        </mat-form-field>
    </mat-card-content>

    <mat-card-actions>
        <button mat-raised-button color="accent"
                (click)="login()">Login</button>
    </mat-card-actions>

    <mat-card-actions>
        <h4>Don't have an account?</h4>
        <a mat-stroked-button color="accent" routerLink="/register">Register</a>
    </mat-card-actions>
</mat-card>