<table mat-table [dataSource]="dataSource">

    <!-- User Info -->
    <ng-container matColumnDef="userId">
        <th mat-header-cell *matHeaderCellDef>User ID</th>
        <td mat-cell *matCellDef="let element"> {{ element.id }} </td>
    </ng-container>

    <ng-container matColumnDef="userName">
        <th mat-header-cell *matHeaderCellDef>Name</th>
        <td mat-cell *matCellDef="let element">
            {{ element.firstName }} {{ element.lastName }}
        </td>
    </ng-container>

    <ng-container matColumnDef="email">
        <th mat-header-cell *matHeaderCellDef>Email</th>
        <td mat-cell *matCellDef="let element">
            {{ element.email }}
        </td>
    </ng-container>

    <ng-container matColumnDef="userType">
        <th mat-header-cell *matHeaderCellDef>Type</th>
        <td mat-cell *matCellDef="let element">
            {{ element.userType }}
        </td>
    </ng-container>

    <ng-container matColumnDef="createdOn">
        <th mat-header-cell *matHeaderCellDef>Created On</th>
        <td mat-cell *matCellDef="let element">
            {{ element.createdOn | date:"dd-MMM-YYYY" }}
        </td>
    </ng-container>

    <ng-container matColumnDef="approve">
        <th mat-header-cell *matHeaderCellDef>Approve</th>
        <td mat-cell *matCellDef="let element">
            <button mat-raised-button color="accent"
                    (click)="this.approve.emit(element)">
                Approve
            </button>
        </td>
    </ng-container>

    <ng-container matColumnDef="mobileNumber">
        <th mat-header-cell *matHeaderCellDef> Mobile Number </th>
        <td mat-cell *matCellDef="let element">
            {{ element.mobileNumber }}
        </td>
    </ng-container>

    <ng-container matColumnDef="accountStatus">
        <th mat-header-cell *matHeaderCellDef> Account Status </th>
        <td mat-cell *matCellDef="let element">
            {{ getAccountStatus(element.accountStatus) }}
        </td>
    </ng-container>

    <ng-container matColumnDef="unblock">
        <th mat-header-cell *matHeaderCellDef> Unblock </th>
        <td mat-cell *matCellDef="let element">
            <button mat-raised-button color="accent"
                    [disabled]="getAccountStatus(element.accountStatus) != 'BLOCKED'"
                    (click)="this.unblock.emit(element)">
                Unblock
            </button>
        </td>
    </ng-container>

    <!-- Order -->
    <ng-container matColumnDef="orderId">
        <th mat-header-cell *matHeaderCellDef>Order ID</th>
        <td mat-cell *matCellDef="let element">{{element.id}}</td>
    </ng-container>

    <ng-container matColumnDef="userIdForOrder">
        <th mat-header-cell *matHeaderCellDef>User ID</th>
        <td mat-cell *matCellDef="let element"> {{ element.userId }} </td>
    </ng-container>

    <ng-container matColumnDef="userNameForOrder">
        <th mat-header-cell *matHeaderCellDef>User Name</th>
        <td mat-cell *matCellDef="let element">{{element.userName}}</td>
    </ng-container>

    <ng-container matColumnDef="bookId">
        <th mat-header-cell *matHeaderCellDef>Book ID</th>
        <td mat-cell *matCellDef="let element">{{element.bookId}}</td>
    </ng-container>

    <ng-container matColumnDef="bookTitle">
        <th mat-header-cell *matHeaderCellDef>Book Title</th>
        <td mat-cell *matCellDef="let element"> {{element.bookTitle}} </td>
    </ng-container>

    <ng-container matColumnDef="orderDate">
        <th mat-header-cell *matHeaderCellDef>Order Date</th>
        <td mat-cell *matCellDef="let element">
            {{element.orderDate | date:"dd-MMM-YYYY"}}
        </td>
    </ng-container>

    <ng-container matColumnDef="fineToPay">
        <th mat-header-cell *matHeaderCellDef>Fine To Pay</th>
        <td mat-cell *matCellDef="let element"> {{getFineToPay(element)}} </td>
    </ng-container>

    <ng-container matColumnDef="returnedDate">
        <th mat-header-cell *matHeaderCellDef>Return Date</th>
        <td mat-cell *matCellDef="let element">
            {{element.returnDate | date:"dd-MMM-YYYY"}}
        </td>
    </ng-container>

    <ng-container matColumnDef="finePaid">
        <th mat-header-cell *matHeaderCellDef>Fine Paid</th>
        <td mat-cell *matCellDef="let element">{{element.finePaid}}</td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="columns"></tr>
    <tr mat-row *matRowDef="let row; columns: columns"></tr>

</table>